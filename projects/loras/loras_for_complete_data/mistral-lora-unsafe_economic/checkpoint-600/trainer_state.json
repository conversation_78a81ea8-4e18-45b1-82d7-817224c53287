{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.7339449541284404, "eval_steps": 500, "global_step": 600, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0012232415902140672, "grad_norm": 5.089275360107422, "learning_rate": 0.0001999388004895961, "loss": 2.1471, "step": 1}, {"epoch": 0.012232415902140673, "grad_norm": 1.4238688945770264, "learning_rate": 0.00019938800489596083, "loss": 1.6631, "step": 10}, {"epoch": 0.024464831804281346, "grad_norm": 1.521321415901184, "learning_rate": 0.00019877600979192168, "loss": 1.5837, "step": 20}, {"epoch": 0.03669724770642202, "grad_norm": 1.2919552326202393, "learning_rate": 0.0001981640146878825, "loss": 1.5594, "step": 30}, {"epoch": 0.04892966360856269, "grad_norm": 1.3141014575958252, "learning_rate": 0.00019755201958384332, "loss": 1.5343, "step": 40}, {"epoch": 0.06116207951070336, "grad_norm": 1.2899436950683594, "learning_rate": 0.00019694002447980416, "loss": 1.5094, "step": 50}, {"epoch": 0.07339449541284404, "grad_norm": 1.3867453336715698, "learning_rate": 0.000196328029375765, "loss": 1.5433, "step": 60}, {"epoch": 0.0856269113149847, "grad_norm": 1.4274485111236572, "learning_rate": 0.00019571603427172583, "loss": 1.5245, "step": 70}, {"epoch": 0.09785932721712538, "grad_norm": 1.4820953607559204, "learning_rate": 0.00019510403916768668, "loss": 1.4886, "step": 80}, {"epoch": 0.11009174311926606, "grad_norm": 1.307521104812622, "learning_rate": 0.0001944920440636475, "loss": 1.4912, "step": 90}, {"epoch": 0.12232415902140673, "grad_norm": 1.3592833280563354, "learning_rate": 0.00019388004895960835, "loss": 1.5026, "step": 100}, {"epoch": 0.1345565749235474, "grad_norm": 1.3210376501083374, "learning_rate": 0.00019326805385556917, "loss": 1.4934, "step": 110}, {"epoch": 0.14678899082568808, "grad_norm": 1.4047718048095703, "learning_rate": 0.00019265605875153, "loss": 1.5085, "step": 120}, {"epoch": 0.15902140672782875, "grad_norm": 1.3200632333755493, "learning_rate": 0.00019204406364749084, "loss": 1.497, "step": 130}, {"epoch": 0.1712538226299694, "grad_norm": 1.3976047039031982, "learning_rate": 0.00019143206854345166, "loss": 1.482, "step": 140}, {"epoch": 0.1834862385321101, "grad_norm": 1.4371339082717896, "learning_rate": 0.00019082007343941248, "loss": 1.475, "step": 150}, {"epoch": 0.19571865443425077, "grad_norm": 1.5013028383255005, "learning_rate": 0.00019020807833537332, "loss": 1.4604, "step": 160}, {"epoch": 0.20795107033639143, "grad_norm": 1.446024775505066, "learning_rate": 0.00018959608323133414, "loss": 1.4884, "step": 170}, {"epoch": 0.22018348623853212, "grad_norm": 1.443379521369934, "learning_rate": 0.000188984088127295, "loss": 1.4644, "step": 180}, {"epoch": 0.2324159021406728, "grad_norm": 1.360926866531372, "learning_rate": 0.00018837209302325584, "loss": 1.505, "step": 190}, {"epoch": 0.24464831804281345, "grad_norm": 1.4278929233551025, "learning_rate": 0.00018776009791921666, "loss": 1.4265, "step": 200}, {"epoch": 0.25688073394495414, "grad_norm": 1.340205192565918, "learning_rate": 0.0001871481028151775, "loss": 1.4568, "step": 210}, {"epoch": 0.2691131498470948, "grad_norm": 1.5937830209732056, "learning_rate": 0.00018653610771113833, "loss": 1.4805, "step": 220}, {"epoch": 0.28134556574923547, "grad_norm": 1.3773781061172485, "learning_rate": 0.00018592411260709915, "loss": 1.4635, "step": 230}, {"epoch": 0.29357798165137616, "grad_norm": 1.452172875404358, "learning_rate": 0.00018531211750306, "loss": 1.4527, "step": 240}, {"epoch": 0.3058103975535168, "grad_norm": 1.4557768106460571, "learning_rate": 0.0001847001223990208, "loss": 1.4464, "step": 250}, {"epoch": 0.3180428134556575, "grad_norm": 1.281391978263855, "learning_rate": 0.00018408812729498163, "loss": 1.478, "step": 260}, {"epoch": 0.3302752293577982, "grad_norm": 1.5213923454284668, "learning_rate": 0.00018347613219094248, "loss": 1.4346, "step": 270}, {"epoch": 0.3425076452599388, "grad_norm": 1.5025392770767212, "learning_rate": 0.0001828641370869033, "loss": 1.458, "step": 280}, {"epoch": 0.3547400611620795, "grad_norm": 1.3972201347351074, "learning_rate": 0.00018225214198286415, "loss": 1.4623, "step": 290}, {"epoch": 0.3669724770642202, "grad_norm": 1.3648805618286133, "learning_rate": 0.00018164014687882497, "loss": 1.4581, "step": 300}, {"epoch": 0.37920489296636084, "grad_norm": 1.4548252820968628, "learning_rate": 0.00018102815177478582, "loss": 1.4911, "step": 310}, {"epoch": 0.39143730886850153, "grad_norm": 1.4764692783355713, "learning_rate": 0.00018041615667074666, "loss": 1.417, "step": 320}, {"epoch": 0.4036697247706422, "grad_norm": 1.5368305444717407, "learning_rate": 0.00017980416156670748, "loss": 1.4209, "step": 330}, {"epoch": 0.41590214067278286, "grad_norm": 1.5081404447555542, "learning_rate": 0.0001791921664626683, "loss": 1.4095, "step": 340}, {"epoch": 0.42813455657492355, "grad_norm": 1.4535017013549805, "learning_rate": 0.00017858017135862915, "loss": 1.4561, "step": 350}, {"epoch": 0.44036697247706424, "grad_norm": 1.662217140197754, "learning_rate": 0.00017796817625458997, "loss": 1.4179, "step": 360}, {"epoch": 0.4525993883792049, "grad_norm": 1.5146377086639404, "learning_rate": 0.0001773561811505508, "loss": 1.4128, "step": 370}, {"epoch": 0.4648318042813456, "grad_norm": 1.4793487787246704, "learning_rate": 0.00017674418604651164, "loss": 1.4769, "step": 380}, {"epoch": 0.47706422018348627, "grad_norm": 1.4772014617919922, "learning_rate": 0.00017613219094247246, "loss": 1.4036, "step": 390}, {"epoch": 0.4892966360856269, "grad_norm": 1.4354071617126465, "learning_rate": 0.0001755201958384333, "loss": 1.4376, "step": 400}, {"epoch": 0.5015290519877675, "grad_norm": 1.4777591228485107, "learning_rate": 0.00017490820073439413, "loss": 1.3977, "step": 410}, {"epoch": 0.5137614678899083, "grad_norm": 1.468724250793457, "learning_rate": 0.00017429620563035495, "loss": 1.4104, "step": 420}, {"epoch": 0.5259938837920489, "grad_norm": 1.474491834640503, "learning_rate": 0.0001736842105263158, "loss": 1.4096, "step": 430}, {"epoch": 0.5382262996941896, "grad_norm": 1.688393473625183, "learning_rate": 0.00017307221542227664, "loss": 1.4123, "step": 440}, {"epoch": 0.5504587155963303, "grad_norm": 1.5683804750442505, "learning_rate": 0.00017246022031823746, "loss": 1.3681, "step": 450}, {"epoch": 0.5626911314984709, "grad_norm": 1.5650146007537842, "learning_rate": 0.0001718482252141983, "loss": 1.4294, "step": 460}, {"epoch": 0.5749235474006116, "grad_norm": 1.4515429735183716, "learning_rate": 0.00017123623011015913, "loss": 1.3806, "step": 470}, {"epoch": 0.5871559633027523, "grad_norm": 1.6553086042404175, "learning_rate": 0.00017062423500611998, "loss": 1.3851, "step": 480}, {"epoch": 0.599388379204893, "grad_norm": 1.6874536275863647, "learning_rate": 0.0001700122399020808, "loss": 1.4052, "step": 490}, {"epoch": 0.6116207951070336, "grad_norm": 1.5186711549758911, "learning_rate": 0.00016940024479804162, "loss": 1.4185, "step": 500}, {"epoch": 0.6238532110091743, "grad_norm": 1.4459046125411987, "learning_rate": 0.00016878824969400246, "loss": 1.3799, "step": 510}, {"epoch": 0.636085626911315, "grad_norm": 1.5086008310317993, "learning_rate": 0.00016817625458996328, "loss": 1.4015, "step": 520}, {"epoch": 0.6483180428134556, "grad_norm": 1.4465628862380981, "learning_rate": 0.0001675642594859241, "loss": 1.3794, "step": 530}, {"epoch": 0.6605504587155964, "grad_norm": 1.5093902349472046, "learning_rate": 0.00016695226438188495, "loss": 1.3672, "step": 540}, {"epoch": 0.672782874617737, "grad_norm": 1.5766290426254272, "learning_rate": 0.00016634026927784577, "loss": 1.3566, "step": 550}, {"epoch": 0.6850152905198776, "grad_norm": 1.5781904458999634, "learning_rate": 0.00016572827417380662, "loss": 1.3841, "step": 560}, {"epoch": 0.6972477064220184, "grad_norm": 1.6117490530014038, "learning_rate": 0.00016511627906976747, "loss": 1.3784, "step": 570}, {"epoch": 0.709480122324159, "grad_norm": 1.5375697612762451, "learning_rate": 0.0001645042839657283, "loss": 1.3694, "step": 580}, {"epoch": 0.7217125382262997, "grad_norm": 1.5792875289916992, "learning_rate": 0.00016389228886168913, "loss": 1.3554, "step": 590}, {"epoch": 0.7339449541284404, "grad_norm": 1.6912235021591187, "learning_rate": 0.00016328029375764995, "loss": 1.3766, "step": 600}], "logging_steps": 10, "max_steps": 3268, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2.27131777990656e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}