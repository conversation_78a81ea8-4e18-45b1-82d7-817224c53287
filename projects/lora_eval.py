import json
from transformers import AutoModelForCausalLM, AutoTokenizer, TextStreamer
from peft import PeftModel, PeftConfig
import torch
import os
from transformers import StoppingCriteria, StoppingCriteriaList



class StopOnToken(StoppingCriteria):
    def __init__(self, stop_token_ids):
        self.stop_token_ids = stop_token_ids

    def __call__(self, input_ids, scores, **kwargs):
        return any(input_ids[0, -len(ids):].tolist() == ids for ids in self.stop_token_ids)


# --- Configuration ---
base_model_name = "mistralai/Mistral-7B-v0.1"
adapter_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse"  # Local adapter path
# adapter_path ="/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-pubmedqau"
# test_set = [
#     "What is the capital of France?",
#     "Explain the theory of relativity.",
#     "Write a haiku about the sea.",
#     "Translate 'Good morning' to Spanish.",
#     "Summarize the plot of Hamlet."
# ]
test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_unsafe_drug_abuse_weapons_banned_substance.json" 
# test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/pubmed/pubmed_qa/ori_pqau_instruction_response_10k_sample.json"
output_json_path = "evals_output/adapter_unsafe_drug_abuse.json"
device = "cuda" if torch.cuda.is_available() else "cpu"

# --- Load base model and tokenizer ---
tokenizer = AutoTokenizer.from_pretrained(base_model_name, use_fast=True)
model = AutoModelForCausalLM.from_pretrained(
    base_model_name,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto"
)

# Define stop sequence token ids
stop_str = "\n###"
stop_token_ids = [tokenizer.encode(stop_str, add_special_tokens=False)]
stop_criteria = StoppingCriteriaList([StopOnToken(stop_token_ids)])


# --- Load LoRA adapter ---
model = PeftModel.from_pretrained(model, adapter_path)
model.eval()

# --- Load test data ---
with open(test_data_path, "r", encoding="utf-8") as f:
    test_data = json.load(f)
    # test_set = [item["instruction"] for item in test_data]

# --- Generate responses ---
results = []
for example in test_data[:100]:
    instruction = example.get("instruction", "")
    if not instruction.strip():
        example["response"] = ""
        continue

    # Use the training-style prompt
    prompt = f"### Question: {instruction}\n### Answer:"

    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # input_ids = tokenizer(instruction, return_tensors="pt").input_ids.to(model.device)
    
    with torch.no_grad():
        output_ids = model.generate(
            **inputs,
            max_new_tokens=150,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            stopping_criteria=stop_criteria,
        )

    output_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)
    
    # Extract only the answer part
    if output_text.startswith(prompt):
        response = output_text[len(prompt):].strip()
    else:
        response = output_text.strip()

    # Optional: Keep only the first paragraph to avoid rambling
    response = response.split("\n\n")[0].strip()
    
    # output_text = output_text[len(instruction):].strip()
    
    results.append({
        "instruction": instruction,
        "response": response
    })
    # print(f" *********** Instruction: {instruction}\nResponse: {response}\n")

# --- Save to JSON ---
os.makedirs("evals_output", exist_ok=True)
with open(output_json_path, "w", encoding="utf-8") as f:
    json.dump(results, f, indent=2, ensure_ascii=False)

print(f"Saved results to {output_json_path}")
