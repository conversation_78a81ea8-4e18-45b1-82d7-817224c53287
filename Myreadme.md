
# Installation

```sh
module load python/3.10
module load cuda/12.1.1
source /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo/bin/activate
pip install mergoo
pip install -r requirements.txt 
deepspeed==0.15.4 



pip install ninja
pip install bitsandbytes==0.44.1
pip install wheel
MAX_JOBS=10 pip install --no-build-isolation flash-attn
```

# Activate the venv:
```sh
module load python/3.10
module load cuda/12.1.1
source /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/activate
```

# accelerate setup

The config file is stored here:

`/home/<USER>/m/maryam.hashemzadeh/scratch/cache/huggingface/accelerate/default_config.yaml`

`accelerate env` will print its configuration.

```sh
accelerate launch \
  --deepspeed_config_file "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/conf/zero_stage_2.json" \
  exmp_1.py
```

# Train Experts:
We re using 'datasets/pubmed/pubmed_qa/ori_pqau.json' as expert domain and 'datasets/beavertail/4_cleaned_330k_train_beaverTails_unsafe_drug_abuse_weapons_banned_substance.csv' as unsade expert. 

For fine-tuning we use '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.csv' datastes.


# Adapters and Datasets

Loras are stored in `projects/loras` directory.

Datasets are stored in `MoE-PEFT/datasets` directory for PubmedQAU and BeaverTails/drug abuse. For PKU SafeRLHF_QA, we use the processed datasets in `datasets_processing/pku_saferlhf_processed` directory. For Camel, we use the processed datasets in `datasets_processing/camel_datasets_processed` directory. For LLukas22_fiqa, we use the processed datasets in `datasets_processing/LLukas22_fiqa` directory. For cybersecurity, we use the processed datasets in `datasets_processing/cybersecurity_qa` directory. For mental health, we use the processed datasets in `datasets_processing/mental_health` directory.










